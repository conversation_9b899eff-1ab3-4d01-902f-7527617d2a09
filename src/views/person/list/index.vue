<template>
  <div v-loading="loading">
    <div class="box">
      <Filter ref="filterRef" @search="handerSearch" />
      <div class="advanced-search">
        <el-button type="primary" @click="showAdvancedSearch"> 高级搜索 </el-button>
        <div class="resume-view-mode">
          <span class="resume-view-label">简历查看方式：</span>
          <el-switch
            v-model="resumeViewModeSwitch"
            active-text="页面查看"
            inactive-text="弹窗查看"
            active-value="page"
            inactive-value="dialog"
            size="default"
          />
        </div>
      </div>
      <el-dialog
        v-model="showAdvancedSearchDialog"
        title="高级搜索"
        width="60%"
        class="search-dialog"
        :close-on-click-modal="false"
      >
        <div class="search-container">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入搜索关键词"
            :prefix-icon="Search"
            @keyup.enter="handleAdvancedSearch"
          >
            <template #append>
              <el-button @click="handleAdvancedSearch" :loading="searchLoading"> 搜索 </el-button>
            </template>
          </el-input>
          <div v-loading="searchLoading" class="search-results">
            <div v-if="searchResults.length > 0">
              <div v-for="item in searchResults" :key="item.resumeId" class="result-item">
                <div class="result-content" v-if="item.educationSchool">
                  <div class="section-title">学校:</div>
                  <div class="content-text" v-html="item.educationSchool"></div>
                </div>

                <!-- researchDirectionContent -->
                <div class="result-content" v-if="item.researchDirectionContent">
                  <div class="section-title">研究方向:</div>
                  <div class="content-text" v-html="item.researchDirectionContent"></div>
                </div>

                <div class="result-content" v-if="item.researchProjectContent">
                  <div class="section-title">研究项目:</div>
                  <div class="content-text" v-html="item.researchProjectContent"></div>
                </div>

                <div class="result-content" v-if="item.academicPaperContent">
                  <div class="section-title">学术论文:</div>
                  <div class="content-text" v-html="item.academicPaperContent"></div>
                </div>

                <div class="result-content" v-if="item.workContent">
                  <div class="section-title">工作内容:</div>
                  <div class="content-text" v-html="item.workContent"></div>
                </div>
              </div>
            </div>
            <div v-else-if="searchKeyword && !searchLoading" class="no-result">
              <el-empty description="未找到相关结果" />
            </div>
          </div>
        </div>
      </el-dialog>
      <div v-loading="tableLoding">
        <div class="jc-between amount">
          <div v-show="!showStat">
            <el-button type="primary" link @click="getStat">查看统计</el-button>
          </div>
          <div v-show="showStat">
            共计:
            <span class="danger">{{ statisticsData.personTotalAmount }}</span>
            个人才&nbsp;&nbsp;站内投递共计:
            <span class="danger">{{ statisticsData.onSiteApplyTotalAmount }}</span>
            次&nbsp;&nbsp;站外应聘共计:
            <span class="danger">{{ statisticsData.offSiteApplyTotalAmount }}</span>
            次&nbsp;&nbsp;约面共计:
            <span class="danger">{{ statisticsData.interviewTotalAmount }}</span
            >次
          </div>
          <el-button type="primary" link @click="showSelectColumn">选择列</el-button>
        </div>

        <CustomColumnDialog
          v-if="showList.length > 0"
          ref="customColumnDialog"
          v-model:data="showList"
          @confirm="updateCustomColumn"
        />

        <TalentDialog ref="talentDialogRef"></TalentDialog>

        <PersonDetailDialog ref="personDetailDialogRef" @refresh="updateList"></PersonDetailDialog>

        <tagDialog
          ref="tagDialogRef"
          :data="tagIds"
          :resumeId="resumeId"
          @resetTagList="resetTagList"
          @confirm="updateList"
        ></tagDialog>

        <el-table :data="list" border size="small" align="center" @sort-change="handleSortChange">
          <el-table-column v-if="showListColumn.includes('uid')" prop="uid" label="UID" />
          <el-table-column v-if="showListColumn.includes('name')" prop="name" label="姓名">
            <template #default="scope">
              <el-button
                type="primary"
                link
                size="small"
                @click="toDetail(scope.row)"
                v-if="scope.row.name"
                >{{ scope.row.name }}</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            v-if="showListColumn.includes('username')"
            prop="username"
            label="用户名"
          />
          <el-table-column
            v-if="showListColumn.includes('baseInfo')"
            prop="baseInfo"
            label="基本信息"
          />
          <el-table-column
            v-if="showListColumn.includes('statusTxt')"
            prop="statusTxt"
            label="账号状态"
          >
            <template #default="{ row }">
              <div class="flex ai-center">
                <span>{{ row.statusTxt }}</span>
                <!-- 测试不同图标 -->
                <el-tooltip :content="row.cancelReason || '暂无注销原因'">
                  <span class="ml-5 cursor-pointer" style="color: #409eff">
                    <el-icon style="font-size: 16px">
                      <InfoFilled />
                    </el-icon>
                    <el-icon style="font-size: 16px">
                      <Warning />
                    </el-icon>
                    <el-icon style="font-size: 16px">
                      <CircleQuestion />
                    </el-icon>
                  </span>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="showListColumn.includes('resumeCompletePercent')"
            prop="resumeCompletePercent"
            label="简历完整度"
            sortable="custom"
          />
          <el-table-column
            v-if="showListColumn.includes('workStatusTxt')"
            prop="workStatusTxt"
            label="求职状态"
          />
          <el-table-column
            v-if="showListColumn.includes('resumeType')"
            prop="resumeType"
            label="简历类型"
          />
          <el-table-column
            prop="onSiteApplyAmount"
            v-if="showListColumn.includes('onSiteApplyAmount')"
            label="站内投递"
            sortable="custom"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                size="small"
                @click="toJobApply(scope.row.memberId, scope.row.onSiteApplyAmount, 1)"
                >{{ scope.row.onSiteApplyAmount }}</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            v-if="showListColumn.includes('offSiteApplyAmount')"
            prop="offSiteApplyAmount"
            label="站外投递"
            sortable="custom"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                size="small"
                @click="toJobApply(scope.row.memberId, scope.row.offSiteApplyAmount, 2)"
                >{{ scope.row.offSiteApplyAmount }}</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            v-if="showListColumn.includes('emailCount')"
            prop="emailCount"
            label="站外投递(邮)"
          >
            <template #default="{ row }">
              <el-button
                type="text"
                size="small"
                @click="toJobApply(row.memberId, row.emailCount, 2)"
                >{{ row.emailCount }}</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            v-if="showListColumn.includes('linkCount')"
            prop="linkCount"
            label="站外投递(网)"
          >
            <template #default="{ row }">
              <el-button
                type="text"
                size="small"
                @click="toJobApply(row.memberId, row.linkCount, 2)"
                >{{ row.linkCount }}</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            v-if="showListColumn.includes('interviewAmount')"
            prop="interviewAmount"
            label="约面(次)"
            sortable="custom"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                size="small"
                @click="toJobApply(scope.row.memberId, scope.row.interviewRecordAmount, 3)"
                >{{ scope.row.interviewRecordAmount }}</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            v-if="showListColumn.includes('resumeDownloadAmount')"
            prop="resumeDownloadAmount"
            label="简历下载"
            sortable="custom"
          />

          <el-table-column
            v-if="showListColumn.includes('resumeLibraryCollectAmount')"
            prop="resumeLibraryCollectAmount"
            label="人才库收藏"
            sortable="custom"
          >
            <template #default="{ row: { resumeId, resumeLibraryCollectAmount } }">
              <el-button
                type="primary"
                link
                size="small"
                @click="handleViewDetail(resumeId, resumeLibraryCollectAmount, 'collect')"
                >{{ resumeLibraryCollectAmount }}</el-button
              >
            </template>
          </el-table-column>

          <el-table-column
            v-if="showListColumn.includes('resumeLibraryDownloadAmount')"
            prop="resumeLibraryDownloadAmount"
            label="人才库下载"
            sortable="custom"
          >
            <template #default="{ row: { resumeId, resumeLibraryDownloadAmount } }">
              <el-button
                type="primary"
                link
                size="small"
                @click="handleViewDetail(resumeId, resumeLibraryDownloadAmount, 'download')"
                >{{ resumeLibraryDownloadAmount }}</el-button
              >
            </template>
          </el-table-column>

          <el-table-column
            v-if="showListColumn.includes('createTime')"
            prop="createTime"
            label="注册时间"
            sortable="custom"
          />
          <el-table-column
            v-if="showListColumn.includes('lastLoginTime')"
            prop="lastLoginTime"
            label="最近登录"
            sortable="custom"
          />
          <el-table-column
            v-if="showListColumn.includes('lastActiveTime')"
            prop="lastActiveTime"
            label="最近活跃"
            sortable="custom"
          />
          <el-table-column
            v-if="showListColumn.includes('sourceTypeTxt')"
            prop="sourceTypeTxt"
            label="注册来源"
          />
          <el-table-column
            v-if="showListColumn.includes('highSchoolName')"
            prop="school"
            label="最高学历毕业学院"
          />
          <el-table-column
            v-if="showListColumn.includes('bindTxt')"
            prop="bindTxt"
            label="是否绑定微信"
          />
          <el-table-column
            v-if="showListColumn.includes('lastUpdateTime')"
            prop="lastUpdateTime"
            label="最近更新时间"
            sortable="custom"
          />
          <el-table-column
            v-if="showListColumn.includes('isSubscribe')"
            prop="isSubscribe"
            label="是否开启订阅"
          />
          <el-table-column
            v-if="showListColumn.includes('refreshTime')"
            prop="refreshTime"
            label="简历刷新时间"
            sortable="custom"
          />
          <el-table-column
            v-if="showListColumn.includes('resumeTypeTag')"
            prop="resumeTypeTag"
            label="简历类型"
          />
          <el-table-column
            v-if="showListColumn.includes('resumeStatusName')"
            prop="resumeStatusName"
            label="简历开放状态"
          />
          <el-table-column
            v-if="showListColumn.includes('vipTypeName')"
            prop="vipTypeName"
            label="会员类型"
          />
          <el-table-column
            v-if="showListColumn.includes('vipExpireTime')"
            prop="vipExpireTime"
            sortable="custom"
            label="会员到期时间"
          />
          <el-table-column
            v-if="showListColumn.includes('ownChatNum')"
            prop="ownChatNum"
            label="主动发起直聊(次)"
            sortable="custom"
          >
            <template #default="{ row }">
              <el-button type="text" size="small" @click="toMyChat(row)">{{
                row.ownChatNum
              }}</el-button>
            </template>
          </el-table-column>
          <el-table-column
            v-if="showListColumn.includes('otherChatNum')"
            prop="otherChatNum"
            label="被动发起直聊(次)"
            sortable="custom"
          >
            <template #default="{ row }">
              <el-button type="text" size="small" @click="toCompanyChat(row)">{{
                row.otherChatNum
              }}</el-button>
            </template>
          </el-table-column>

          <el-table-column
            v-if="showListColumn.includes('isChat')"
            label="是否开启直聊功能"
            prop="isChat"
          >
            <template #default="{ row }">
              <el-switch
                v-model="row.isChat"
                active-value="1"
                inactive-value="2"
                @click="beforeChange(row.isChat, row.memberId)"
              />
            </template>
          </el-table-column>

          <el-table-column
            v-if="showListColumn.includes('resumeTag')"
            prop="resumeTag"
            label="人才标签"
          />

          <el-table-column label="操作" fixed="right">
            <template #default="scope">
              <el-button type="primary" link size="small" @click="showLog(scope.row)">
                查看日志
              </el-button>
              <el-button type="primary" link size="small" @click="changeMemberStatus(scope.row)">
                {{ scope.row.status == 1 ? '禁用' : '启用' }}
              </el-button>
              <el-button type="primary" link size="small" @click="toDetail(scope.row)"
                >简历</el-button
              >
              <el-button type="primary" link size="small" @click="vipInfo(scope.row)"
                >会员信息</el-button
              >
              <el-button type="primary" link size="small" @click="handleTag(scope.row)"
                >贴标签</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="paging">
          <Paging :total="pagination.total" @change="changePage"></Paging>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, ref, watch, onMounted, toRaw, defineComponent, unref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Filter from '../components/filter.vue'
import CustomColumnDialog from '../components/customColumnDialog.vue'
import TalentDialog from '../components/talentDialog.vue'
import PersonDetailDialog from '../components/personDetailDialog.vue'
import tagDialog from '../components/tagDialog.vue'
import {
  Search,
  InfoFilled,
  QuestionFilled,
  Warning,
  CircleQuestion
} from '@element-plus/icons-vue'

import { getTableStagingField, setTableStagingField } from '/@/api/config'
import { getList, getListStatistics, getResumeV2 } from '/@/api/person'
import Paging from '/@/components/base/paging.vue'
import router from '/@/router'
import { lockAccount, unlockAccount } from '/@/api/member'
import { editChatConfig } from '/@/api/account'

export default defineComponent({
  name: 'personList',
  components: {
    Filter,
    CustomColumnDialog,
    Paging,
    TalentDialog,
    PersonDetailDialog,
    tagDialog,
    Search,
    InfoFilled,
    QuestionFilled,
    Warning,
    CircleQuestion
  },
  setup() {
    const filterRef = ref()
    const customColumnDialog = ref()
    const talentDialogRef = ref()
    const personDetailDialogRef = ref()
    const tagDialogRef = ref()
    const state = reactive({
      loading: false,
      formData: {
        page: 1,
        pageSize: 20,
        sortCreateTime: '',
        sortLastLoginTime: '',
        sortLastActiveTime: '',
        sortOnSiteApplyAmount: '',
        sortOffSiteApplyAmount: '',
        sortDownloadAmount: '',
        sortInterviewAmount: '',
        sortLastUpdateTime: '',
        sortRefreshTime: '',
        sortComplete: '',
        sortVipExpireTime: '',
        sortResumeLibraryCollectAmount: '',
        sortResumeLibraryDownloadAmount: '',
        sortLinkCount: '',
        sortEmailCount: '',
        sortOwnChatNum: '',
        sortOtherChatNum: ''
      },
      pagination: {
        total: 0,
        limit: 20,
        page: 1
      },
      list: [],
      showList: <any>[],
      showListColumn: <any>[],
      showStat: false,
      tableLoding: false,
      statisticsData: {
        personTotalAmount: '',
        onSiteApplyTotalAmount: '',
        offSiteApplyTotalAmount: '',
        interviewTotalAmount: '',
        emailCount: '',
        linkCount: ''
      },
      tagIds: [],
      resumeId: '',
      showAdvancedSearchDialog: false,
      searchKeyword: '',
      searchResults: [],
      searchLoading: false,
      resumeViewModeSwitch: 'dialog', // 简历查看方式：dialog-弹窗，page-页面
      handleViewDetail(id, amount = 0, type) {
        if (amount <= 0) {
          return
        }

        unref(talentDialogRef).show(id, type)
      }
    })
    const tableName = 'personList'
    const baseShowList = [
      { k: 'uid', v: 'UID', select: true, default: true },
      { k: 'name', v: '姓名', select: true, default: true },
      { k: 'username', v: '用户名', select: true, default: true },
      { k: 'baseInfo', v: '基本信息', select: true, default: true },
      { k: 'statusTxt', v: '账号状态', select: true, default: true },
      { k: 'resumeCompletePercent', v: '简历完整度', select: true, default: true },
      { k: 'workStatusTxt', v: '求职状态', select: true, default: true },
      { k: 'onSiteApplyAmount', v: '站内投递', select: true, default: true },
      { k: 'offSiteApplyAmount', v: '站外投递', select: true, default: true },
      { k: 'interviewAmount', v: '约面(次)', select: true, default: true },
      { k: 'resumeDownloadAmount', v: '简历下载', select: true, default: true },
      { k: 'resumeLibraryCollectAmount', v: '人才库收藏数量', select: true, default: true },
      { k: 'resumeLibraryDownloadAmount', v: '人才库下载数量', select: true, default: true },
      { k: 'createTime', v: '注册时间', select: true, default: true },
      { k: 'lastLoginTime', v: '最近登录', select: true, default: true },
      { k: 'lastActiveTime', v: '最近活跃', select: true, default: true },
      { k: 'sourceTypeTxt', v: '注册来源', select: true, default: false },
      { k: 'highSchoolName', v: '最高学历毕业学院', select: true, default: false },
      { k: 'bindTxt', v: '是否绑定微信', select: true, default: false },
      { k: 'lastUpdateTime', v: '最近更新时间', select: true, default: false },
      { k: 'emailCount', v: '站外投递(邮)', select: true, default: false },
      { k: 'linkCount', v: '站外投递(网)', select: true, default: false },
      { k: 'isSubscribe', v: '是否开启订阅', select: true, default: false },
      { k: 'refreshTime', v: '简历刷新时间', select: true, default: false },
      { k: 'resumeTypeTag', v: '简历类型', select: true, default: false },
      { k: 'resumeStatusName', v: '简历开放状态', select: true, default: false },
      { k: 'vipTypeName', v: '会员类型', select: true, default: false },
      { k: 'vipExpireTime', v: '会员到期时间', select: true, default: false },
      { k: 'ownChatNum', v: '主动发起直聊(次)', select: true, default: false },
      { k: 'otherChatNum', v: '被动发起直聊(次)', select: true, default: false },
      { k: 'isChat', v: '是否开启直聊功能', select: true, default: false },
      { k: 'resumeTag', v: '人才标签', select: true, default: false },
      { k: 'resumeType', v: '简历类型', select: true, default: false }
    ]
    const setShowList = (keys: string) => {
      const keysArr = keys.split(',')
      if (keysArr.length === 0) {
        return
      }
      state.showList = baseShowList.map((item) => {
        let select = false
        if (keysArr.includes(item.k)) {
          select = true
        }
        return {
          k: item.k,
          v: item.v,
          default: item.default,
          select
        }
      })
    }

    watch(
      () => state.showList,
      (newVal: any) => {
        state.showListColumn = newVal.reduce((a: any, b: any) => {
          if (b.select) {
            a.push(b.k)
          }
          return a
        }, [])
      },
      { immediate: true }
    )
    const handleSortable = (data: any) => {}

    function getStat() {
      getListStatistics(state.formData).then((res: any) => {
        state.statisticsData = res
        state.showStat = true
      })
    }

    const search = () => {
      state.tableLoding = true
      const params = toRaw(state.formData)
      getList(params).then((r) => {
        state.list = r.list
        state.tableLoding = false
        state.pagination.total = r.page.count * 1
      })
    }

    const beforeChange = (isChat: string, memberId: string) => {
      const chat = isChat === '2'
      const text = chat ? '关闭' : '开启'

      ElMessageBox.confirm(
        `确定${text}直聊服务吗？${text}后，求职者${chat ? '将不能' : '可'}与单位进行直聊沟通。`,
        '提示'
      )
        .then(async () => {
          await editChatConfig({ isChat, memberId })
          search()
        })
        .catch(() => {
          search()
        })
    }

    function getStat() {
      getListStatistics(state.formData).then((res: any) => {
        state.statisticsData = res
        state.showStat = true
      })
    }

    const handleReset = () => {
      state.formData = {
        page: 1,
        pageSize: 20,
        sortCreateTime: '',
        sortLastLoginTime: '',
        sortLastActiveTime: '',
        sortOnSiteApplyAmount: '',
        sortOffSiteApplyAmount: '',
        sortDownloadAmount: '',
        sortInterviewAmount: '',
        sortLastUpdateTime: '',
        sortRefreshTime: '',
        sortComplete: '',
        sortVipExpireTime: '',
        sortResumeLibraryCollectAmount: '',
        sortResumeLibraryDownloadAmount: '',
        sortLinkCount: '',
        sortEmailCount: '',
        sortOwnChatNum: '',
        sortOtherChatNum: ''
      }
      search()
    }

    const handerSearch = (data: any) => {
      state.formData = {
        ...state.formData,
        ...data
      }
      search()
    }

    const showLog = (row: any) => {
      if (!row.memberId) {
        ElMessage.error('非法账号')
        return
      }
      router.push({
        path: '/member/logList',
        query: { id: row.memberId, isShow: 1 }
      })
    }

    const updateList = () => {
      search()
    }

    const resetTagList = () => {
      filterRef.value.getResumeTagListFun()
    }

    const handleTag = (row: any) => {
      state.resumeId = row.resumeId
      state.tagIds = row.resumeTagId
      tagDialogRef.value.open()
    }

    const vipInfo = (row: any) => {
      if (!row.resumeId) {
        ElMessage.error('参数错误')
        return
      }
      router.push({
        path: '/member/vipInfo',
        query: { id: row.resumeId, isShow: 1 }
      })
    }

    const lockAccountShow = (row: any) => {
      if (!row.memberId) {
        ElMessage.error('非法账号')
        return
      }
      ElMessageBox.confirm('确定要禁用该人才吗？', '提示').then(async () => {
        lockAccount(row.memberId)
      })
    }

    const unlockAccountShow = (row: any) => {
      if (!row.memberId) {
        ElMessage.error('非法账号')
        return
      }
      ElMessageBox.confirm('确定要启用该人才吗？', '提示').then(async () => {
        unlockAccount(row.memberId)
      })
    }

    const showSelectColumn = () => {
      customColumnDialog.value.open()
    }

    const changePage = (r) => {
      state.formData.page = r.page
      state.formData.pageSize = r.limit
      search()
    }

    const handleSortChange = ({ prop, order }) => {
      let orderValue = ''
      if (order === 'ascending') {
        orderValue = '1'
      } else if (order === 'descending') {
        orderValue = '2'
      }

      switch (prop) {
        case 'linkCount':
          state.formData.sortLinkCount = orderValue
          break
        case 'emailCount':
          state.formData.sortEmailCount = orderValue
          break
        case 'onSiteApplyAmount':
          state.formData.sortOnSiteApplyAmount = orderValue
          break
        case 'offSiteApplyAmount':
          state.formData.sortOffSiteApplyAmount = orderValue
          break
        case 'interviewAmount':
          state.formData.sortInterviewAmount = orderValue
          break
        case 'resumeDownloadAmount':
          state.formData.sortDownloadAmount = orderValue
          break
        case 'createTime':
          state.formData.sortCreateTime = orderValue
          break
        case 'lastLoginTime':
          state.formData.sortLastLoginTime = orderValue
          break
        case 'lastActiveTime':
          state.formData.sortLastActiveTime = orderValue
          break
        case 'sourceTypeTxt':
          break
        case 'lastUpdateTime':
          state.formData.sortLastUpdateTime = orderValue
          break
        case 'refreshTime':
          state.formData.sortRefreshTime = orderValue
          break
        case 'resumeCompletePercent':
          state.formData.sortComplete = orderValue
          break
        case 'resumeLibraryCollectAmount':
          state.formData.sortResumeLibraryCollectAmount = orderValue
          break
        case 'resumeLibraryDownloadAmount':
          state.formData.sortResumeLibraryDownloadAmount = orderValue
          break
        case 'vipExpireTime':
          state.formData.sortVipExpireTime = orderValue
          break
        case 'ownChatNum':
          state.formData.sortOwnChatNum = orderValue
          break
        case 'otherChatNum':
          state.formData.sortOtherChatNum = orderValue
          break

        default:
          break
      }
      search()
    }

    const updateCustomColumn = (r: any) => {
      state.showList = r
      setTimeout(() => {
        const value = state.showListColumn.join(',')
        setTableStagingField(tableName, value)
      }, 300)
    }

    const changeMemberStatus = (row: any) => {
      const txt = row.status === '1' ? '你确定禁用该人才' : '你确定启动该人才'
      ElMessageBox.confirm(txt, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          if (row.status === '1') {
            await lockAccount(row.memberId)
          } else {
            await unlockAccount(row.memberId)
          }
          search()
        })
        .catch(() => {})
    }

    const toJobApply = (memberId, amount = 0, tag = 1) => {
      if (!memberId) {
        ElMessage.error('非法账号')
        return
      }
      if (amount <= 0) {
        return
      }
      router.push(`/person/jobApplyDetail/${memberId}?tag=${tag}`)
    }
    const toDetail = (row) => {
      if (!row.memberId) {
        ElMessage.error('非法账号')
        return
      }

      if (state.resumeViewModeSwitch === 'page') {
        // 新开tab
        const url = router.resolve({ path: `/person/detail/${row.memberId}` })
        window.open(url.href, '_blank')
      } else {
        // 弹窗模式
        // 找到当前行在列表中的索引
        const currentIndex = state.list.findIndex((item) => item.memberId === row.memberId)

        // 获取当前的筛选条件（排除分页和排序相关的字段）
        const filterConditions = { ...state.formData }
        delete filterConditions.page
        delete filterConditions.pageSize
        // 删除所有排序字段
        Object.keys(filterConditions).forEach((key) => {
          if (key.startsWith('sort')) {
            delete filterConditions[key]
          }
        })

        // 打开人才详情弹窗，传递列表数据、当前索引和筛选条件
        personDetailDialogRef.value.open(row.memberId, state.list, currentIndex, filterConditions)
      }
    }

    const toMyChat = (row) => {
      if (!row.memberId) {
        ElMessage.error('非法账号')
        return
      }
      router.push(`/configuration/chat?resumeId=${row.uid}&creatorType=1`)
    }

    const toCompanyChat = (row) => {
      if (!row.memberId) {
        ElMessage.error('非法账号')
        return
      }
      router.push(`/configuration/chat?resumeId=${row.uid}&creatorType=2`)
    }

    onMounted(async () => {
      const keys = await getTableStagingField(tableName)
      setShowList(keys.value)
      ElMessage({
        message: '由于数据过多,默认不显示数据,请自行搜索',
        type: 'warning',
        duration: 5000
      })
    })

    const handleAdvancedSearch = async () => {
      if (!state.searchKeyword) return

      state.searchLoading = true
      try {
        const res = await getResumeV2({ keyword: state.searchKeyword })
        state.searchResults = res.list || []
        const resultsEl = document.querySelector('.search-results')
        if (resultsEl) {
          resultsEl.scrollTop = 0
        }
      } catch (err) {
        console.error(err)
      }
      state.searchLoading = false
    }

    const showAdvancedSearch = () => {
      // 提醒用户这是一个实验性功能，可能会有一些问题，请谨慎使用
      ElMessageBox.confirm('这是一个实验性功能，可能会有一些问题，请谨慎使用', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          state.showAdvancedSearchDialog = true
        })
        .catch(() => {})
    }

    return {
      getList,
      handleReset,
      handerSearch,
      talentDialogRef,
      personDetailDialogRef,
      tagDialogRef,
      customColumnDialog,
      filterRef,
      updateList,
      resetTagList,
      showLog,
      vipInfo,
      handleTag,
      getStat,
      showSelectColumn,
      updateCustomColumn,
      handleSortChange,
      changePage,
      lockAccountShow,
      toJobApply,
      toDetail,
      unlockAccountShow,
      changeMemberStatus,
      beforeChange,
      toMyChat,
      toCompanyChat,
      handleAdvancedSearch,
      showAdvancedSearch,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="scss">
.title {
  border-left: 2px solid #196bf9;
  text-indent: 1em;
}
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
}

.advanced-search {
  display: flex;
  align-items: center;
  margin-top: 16px;
  padding: 12px 0;

  .resume-view-mode {
    margin-left: 12px;
    display: flex;
    align-items: center;

    .resume-view-label {
      margin-right: 8px;
      font-size: 14px;
      color: #606266;
    }
  }
}
.amount {
  margin: 20px 0;
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  background-color: #edf9ff;
  .danger {
    color: #d9041a;
    font-weight: bold;
  }
}
.paging {
  margin-top: 30px;
}
.advanced-search {
  margin-bottom: 20px;
}
.search-container {
  .el-input {
    .el-input__wrapper {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  .search-results {
    margin-top: 20px;
    max-height: 60vh;
    overflow-y: auto;
    min-height: 200px;
    padding: 10px;

    .result-item {
      padding: 15px;
      margin-bottom: 15px;
      border-bottom: 1px solid #eee;
      background: #f8f9fa;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }

      &:hover {
        background: #f0f2f5;
        transform: translateY(-2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .result-content {
        margin-top: 10px;

        .section-title {
          font-weight: bold;
          color: #666;
          margin-bottom: 5px;
          font-size: 14px;
        }

        .content-text {
          color: #333;
          line-height: 1.5;
          font-size: 13px;
          padding: 8px;
          background: #fff;
          border-radius: 4px;

          :deep(em) {
            color: #409eff;
            font-style: normal;
            font-weight: bold;
          }
        }
      }
    }
  }

  .no-result {
    text-align: center;
    color: #999;
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
  }
}

:deep(.search-dialog) {
  .el-dialog__header {
    margin: 0;
    padding: 20px;
    border-bottom: 1px solid #eee;
  }

  .el-dialog__body {
    padding: 20px;
  }
}

.ml-5 {
  margin-left: 5px;
}

.cursor-pointer {
  cursor: pointer;
}

.text-warning {
  color: #e6a23c;
}
</style>
