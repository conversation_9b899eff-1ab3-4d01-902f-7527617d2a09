<template>
  <div class="talent-analysis-page">
    <div class="page-header">
      <h1>外籍人才分析工具</h1>
      <p>输入简历ID，快速分析人才信息，协助运营判断</p>
    </div>
    
    <!-- 输入区域 -->
    <el-card class="input-card">
      <div class="input-section">
        <el-input
          v-model="resumeId"
          placeholder="请输入简历ID"
          size="large"
          style="width: 300px; margin-right: 15px;"
          @keyup.enter="handleAnalyze"
        />
        <el-button 
          type="primary" 
          size="large"
          @click="handleAnalyze"
          :loading="analyzing"
        >
          <el-icon><Search /></el-icon>
          分析
        </el-button>
      </div>
    </el-card>
    
    <!-- 结果展示 -->
    <div v-if="result" class="result-section">
      <!-- 基本信息卡片 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>👤 人才基本信息</span>
          </div>
        </template>
        <div class="talent-basic">
          <el-avatar :src="result.talentInfo.avatar" :size="80">
            {{ result.talentInfo.name?.charAt(0) }}
          </el-avatar>
          <div class="basic-info">
            <h2>{{ result.talentInfo.name }} 
              <span v-if="result.talentInfo.englishName" class="english-name">
                ({{ result.talentInfo.englishName }})
              </span>
            </h2>
            <div class="info-row">
              <el-tag>{{ result.talentInfo.age }}岁</el-tag>
              <el-tag type="info">{{ result.talentInfo.gender }}</el-tag>
            </div>
            <div class="info-row">
              <span><strong>户籍:</strong> {{ result.talentInfo.householdRegister }}</span>
              <span><strong>居住地:</strong> {{ result.talentInfo.residence }}</span>
            </div>
          </div>
        </div>
      </el-card>
      
      <!-- 分析结果卡片 -->
      <el-card class="analysis-card">
        <template #header>
          <div class="card-header">
            <span>🤖 AI分析结果</span>
          </div>
        </template>
        
        <div class="analysis-result">
          <!-- 主要结果 -->
          <div class="result-main">
            <div class="result-badge">
              <el-tag 
                :type="result.analysisResult.isForeign ? 'danger' : 'success'"
                size="large"
                class="result-tag"
              >
                {{ result.analysisResult.isForeign ? '🌍 外籍人才' : '🏠 本国人才' }}
              </el-tag>
            </div>
            <div class="confidence-info">
              <div class="confidence-bar">
                <el-progress 
                  :percentage="Math.round(result.analysisResult.confidence * 100)"
                  :color="getConfidenceColor(result.analysisResult.confidence)"
                  :stroke-width="12"
                  :show-text="false"
                />
              </div>
              <div class="confidence-text">
                <span class="confidence-value">
                  置信度: {{ Math.round(result.analysisResult.confidence * 100) }}%
                </span>
                <el-tag 
                  :type="getConfidenceTagType(result.analysisResult.level)"
                  size="small"
                >
                  {{ result.analysisResult.level }}置信度
                </el-tag>
              </div>
            </div>
          </div>
          
          <!-- 分析依据 -->
          <div class="analysis-reasons">
            <h3>📋 分析依据</h3>
            <ul class="reasons-list">
              <li v-for="(reason, index) in result.analysisReasons" :key="index">
                <el-icon class="reason-icon"><Check /></el-icon>
                {{ reason }}
              </li>
            </ul>
          </div>
          
          <!-- 运营建议 -->
          <div class="suggestion">
            <h3>💡 运营建议</h3>
            <el-alert
              :title="result.analysisResult.suggestion"
              :type="result.analysisResult.isForeign ? 'warning' : 'success'"
              :closable="false"
              show-icon
            />
          </div>
        </div>
      </el-card>
      
      <!-- 详细信息卡片 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <span>📄 详细信息</span>
            <el-button 
              text 
              @click="showDetail = !showDetail"
              class="toggle-btn"
            >
              {{ showDetail ? '收起' : '展开' }}
              <el-icon>
                <ArrowDown v-if="!showDetail" />
                <ArrowUp v-else />
              </el-icon>
            </el-button>
          </div>
        </template>
        
        <el-collapse-transition>
          <div v-show="showDetail" class="detail-content">
            <!-- 教育经历 -->
            <div class="detail-section">
              <h4><el-icon><School /></el-icon> 教育经历</h4>
              <div v-if="result.detailInfo.education.length === 0" class="no-data">
                暂无教育经历信息
              </div>
              <div v-else>
                <div 
                  v-for="(edu, index) in result.detailInfo.education" 
                  :key="index" 
                  class="detail-item"
                >
                  <div class="item-content">
                    <strong>{{ edu.school }}</strong>
                    <span class="item-meta">{{ edu.major }} · {{ edu.degree }}</span>
                    <el-tag v-if="edu.isAbroad" type="warning" size="small">海外</el-tag>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 工作经历 -->
            <div class="detail-section">
              <h4><el-icon><Briefcase /></el-icon> 工作经历</h4>
              <div v-if="result.detailInfo.workExperience.length === 0" class="no-data">
                暂无工作经历信息
              </div>
              <div v-else>
                <div 
                  v-for="(work, index) in result.detailInfo.workExperience" 
                  :key="index" 
                  class="detail-item"
                >
                  <div class="item-content">
                    <strong>{{ work.company }}</strong>
                    <span class="item-meta">{{ work.position }} · {{ work.duration }}</span>
                    <el-tag v-if="work.isAbroad" type="warning" size="small">海外</el-tag>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 语言能力 -->
            <div class="detail-section">
              <h4><el-icon><ChatDotRound /></el-icon> 语言能力</h4>
              <div class="language-tags">
                <el-tag 
                  v-for="lang in result.detailInfo.languages" 
                  :key="lang"
                  class="language-tag"
                >
                  {{ lang }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-collapse-transition>
      </el-card>
    </div>
    
    <!-- 空状态 -->
    <el-empty 
      v-if="!result && !analyzing" 
      description="输入简历ID开始分析"
      :image-size="120"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Search, Check, ArrowDown, ArrowUp, 
  School, Briefcase, ChatDotRound 
} from '@element-plus/icons-vue'

// 响应式数据
const resumeId = ref('')
const analyzing = ref(false)
const result = ref(null)
const showDetail = ref(false)

// 方法
const handleAnalyze = async () => {
  if (!resumeId.value.trim()) {
    ElMessage.warning('请输入简历ID')
    return
  }
  
  // 验证是否为数字
  if (!/^\d+$/.test(resumeId.value.trim())) {
    ElMessage.warning('简历ID必须为数字')
    return
  }
  
  analyzing.value = true
  result.value = null
  showDetail.value = false
  
  try {
    // 模拟API调用
    const response = await mockAnalyzeAPI(parseInt(resumeId.value.trim()))
    result.value = response
    
    ElMessage.success('分析完成')
  } catch (error) {
    ElMessage.error(error.message || '分析失败，请检查简历ID是否正确')
  } finally {
    analyzing.value = false
  }
}

const getConfidenceColor = (confidence) => {
  if (confidence >= 0.8) return '#67c23a'
  if (confidence >= 0.6) return '#e6a23c'
  return '#f56c6c'
}

const getConfidenceTagType = (level) => {
  switch (level) {
    case '高': return 'success'
    case '中': return 'warning'
    case '低': return 'danger'
    default: return 'info'
  }
}

// 模拟API调用（实际使用时替换为真实API）
const mockAnalyzeAPI = async (resumeId) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 模拟不同的分析结果
      const mockResults = {
        12345: {
          resumeId: 12345,
          talentInfo: {
            name: "张三",
            englishName: "Zhang San",
            age: 28,
            gender: "男",
            householdRegister: "北京市",
            residence: "上海市",
            avatar: ""
          },
          analysisResult: {
            isForeign: false,
            confidence: 0.85,
            level: "高",
            suggestion: "该人才为本国人才的可能性很高"
          },
          analysisReasons: [
            "姓名为典型中文姓名",
            "户籍为国内城市",
            "教育背景均为国内院校",
            "工作经历均在国内"
          ],
          detailInfo: {
            education: [
              {
                school: "北京大学",
                major: "计算机科学与技术",
                degree: "本科",
                isAbroad: false
              }
            ],
            workExperience: [
              {
                company: "腾讯科技",
                position: "软件工程师",
                duration: "2020-2023",
                isAbroad: false
              }
            ],
            languages: ["中文(母语)", "英语(熟练)"]
          }
        },
        54321: {
          resumeId: 54321,
          talentInfo: {
            name: "阿里·萨米尔",
            englishName: "Ali Samir",
            age: 25,
            gender: "男",
            householdRegister: "未填写",
            residence: "深圳市",
            avatar: ""
          },
          analysisResult: {
            isForeign: true,
            confidence: 0.92,
            level: "高",
            suggestion: "该人才为外籍人才的可能性很高，建议重点关注"
          },
          analysisReasons: [
            "姓名包含外籍关键词：阿里、萨米尔",
            "教育背景包含海外院校",
            "工作经历包含海外公司"
          ],
          detailInfo: {
            education: [
              {
                school: "新加坡国立大学",
                major: "软件工程",
                degree: "硕士",
                isAbroad: true
              }
            ],
            workExperience: [
              {
                company: "Google Singapore",
                position: "Software Developer",
                duration: "2021-2023",
                isAbroad: true
              }
            ],
            languages: ["阿拉伯语(母语)", "英语(流利)", "中文(良好)"]
          }
        }
      }
      
      const result = mockResults[resumeId]
      if (result) {
        resolve(result)
      } else {
        reject(new Error('简历不存在或无权限访问'))
      }
    }, 1500) // 模拟网络延迟
  })
}
</script>

<style scoped>
.talent-analysis-page {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  color: #303133;
  margin-bottom: 10px;
}

.page-header p {
  color: #909399;
  font-size: 14px;
}

.input-card {
  margin-bottom: 30px;
}

.input-section {
  text-align: center;
  padding: 20px;
}

.result-section {
  margin-top: 20px;
}

.info-card, .analysis-card, .detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.toggle-btn {
  display: flex;
  align-items: center;
  gap: 4px;
}

.talent-basic {
  display: flex;
  align-items: center;
  gap: 20px;
}

.basic-info h2 {
  margin: 0 0 15px 0;
  color: #303133;
}

.english-name {
  color: #909399;
  font-weight: normal;
  font-size: 16px;
}

.info-row {
  margin: 10px 0;
  display: flex;
  gap: 15px;
  align-items: center;
}

.analysis-result {
  text-align: center;
}

.result-main {
  margin-bottom: 30px;
}

.result-badge {
  margin-bottom: 20px;
}

.result-tag {
  font-size: 18px;
  padding: 12px 24px;
}

.confidence-info {
  max-width: 400px;
  margin: 0 auto;
}

.confidence-bar {
  margin-bottom: 10px;
}

.confidence-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.confidence-value {
  font-weight: bold;
  color: #606266;
}

.analysis-reasons, .suggestion {
  text-align: left;
  margin: 30px 0;
}

.analysis-reasons h3, .suggestion h3 {
  margin-bottom: 15px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.reasons-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.reasons-list li {
  margin: 12px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
}

.reason-icon {
  color: #67c23a;
  font-size: 16px;
}

.detail-section {
  margin-bottom: 25px;
}

.detail-section h4 {
  margin-bottom: 15px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.detail-item {
  margin: 12px 0;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.item-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.item-meta {
  color: #909399;
  font-size: 14px;
}

.language-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.language-tag {
  margin: 2px;
}

.no-data {
  color: #c0c4cc;
  font-style: italic;
  text-align: center;
  padding: 20px;
}
</style>
